"""
数据加载器 - 简化版本
负责加载机柜配置、典型回路、IODB和PIDB数据
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

from core.logger import get_logger
from core.data_models import IOPoint, SignalType
from utils.excel_utils_simple import ExcelReader


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        self.excel_reader = ExcelReader()
        
        # 获取数据路径配置
        self.data_paths = config.get('data_paths', {})
        self.cabinet_profiles_path = self.data_paths.get('cabinet_profiles', 'data/cabinet_profiles')
        self.wiring_typical_path = self.data_paths.get('wiring_typical', 'data/wiring_typical')
        self.iodb_path = self.data_paths.get('iodb', 'data/iodb')
        self.pidb_path = self.data_paths.get('pidb', 'data/pidb')
    
    def load_all_data(self) -> Dict[str, Any]:
        """
        加载所有配置数据
        
        Returns:
            包含所有数据的字典
        """
        self.logger.info("开始加载所有数据")
        
        result = {
            'cabinet_profiles': {},
            'wiring_typicals': {},
            'load_status': {
                'cabinet_profiles': False,
                'wiring_typicals': False,
                'iodb': False,
                'pidb': False
            }
        }
        
        try:
            # 加载机柜配置
            cabinet_profiles = self.load_cabinet_profiles()
            if cabinet_profiles:
                result['cabinet_profiles'] = cabinet_profiles
                result['load_status']['cabinet_profiles'] = True
            
            # 加载典型回路
            wiring_typicals = self.load_wiring_typicals()
            if wiring_typicals:
                result['wiring_typicals'] = wiring_typicals
                result['load_status']['wiring_typicals'] = True
            
            self.logger.info("数据加载完成")
            return result
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            raise
    
    def load_cabinet_profiles(self) -> Dict[str, Any]:
        """
        加载机柜配置文件
        
        Returns:
            机柜配置字典
        """
        self.logger.info(f"加载机柜配置: {self.cabinet_profiles_path}")
        
        cabinet_profiles = {}
        cabinet_path = Path(self.cabinet_profiles_path)
        
        if not cabinet_path.exists():
            self.logger.warning(f"机柜配置目录不存在: {cabinet_path}")
            return cabinet_profiles
        
        # 查找JSON配置文件
        json_files = list(cabinet_path.glob("*.json"))
        self.logger.info(f"找到{len(json_files)}个机柜配置文件")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    cabinet_name = json_file.stem
                    cabinet_profiles[cabinet_name] = config_data
                    self.logger.debug(f"加载机柜配置: {cabinet_name}")
            except Exception as e:
                self.logger.error(f"加载机柜配置文件失败 {json_file}: {e}")
        
        self.logger.info("机柜配置文件加载成功")
        return cabinet_profiles
    
    def load_wiring_typicals(self) -> Dict[str, Any]:
        """
        加载典型回路文件
        
        Returns:
            典型回路字典
        """
        self.logger.info(f"加载典型回路: {self.wiring_typical_path}")
        
        wiring_typicals = {}
        typical_path = Path(self.wiring_typical_path)
        
        if not typical_path.exists():
            self.logger.warning(f"典型回路目录不存在: {typical_path}")
            return wiring_typicals
        
        # 查找XML文件
        xml_files = list(typical_path.glob("*.xml"))
        self.logger.info(f"找到{len(xml_files)}个典型回路文件")
        
        for xml_file in xml_files:
            try:
                # 简化处理，只记录文件信息
                typical_name = xml_file.stem
                wiring_typicals[typical_name] = {
                    'file_path': str(xml_file),
                    'name': typical_name,
                    'components': []  # 实际解析需要XML处理
                }
                self.logger.debug(f"加载典型回路: {typical_name}")
            except Exception as e:
                self.logger.error(f"加载典型回路文件失败 {xml_file}: {e}")
        
        self.logger.info("典型回路文件加载成功")
        return wiring_typicals
    
    def load_iodb_data(self, file_path: str) -> Dict[str, Any]:
        """
        加载IODB数据
        
        Args:
            file_path: IODB文件路径
            
        Returns:
            IODB数据字典
        """
        self.logger.info(f"加载IODB数据: {file_path}")
        
        try:
            # 使用Excel读取器读取文件
            excel_data = self.excel_reader.read_excel_file(file_path)
            
            # 处理Excel数据
            io_points = []
            cables = {}
            
            # 假设第一个工作表包含IODB数据
            if isinstance(excel_data, dict):
                sheet_name = list(excel_data.keys())[0]
                df = excel_data[sheet_name]
            else:
                df = excel_data
            
            self.logger.info(f"IODB工作表包含 {len(df)} 行数据")
            
            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    # 创建IOPoint对象
                    io_point = IOPoint(
                        tag=str(row.get('Tag', '')),
                        signal_type=self._parse_signal_type(str(row.get('Signal_Type', 'DI'))),
                        description=str(row.get('Description', '')),
                        location=str(row.get('Location', '')),
                        cabinet=str(row.get('Cabinet', '')),
                        rack=str(row.get('Rack', '')),
                        slot=str(row.get('Slot', '')),
                        channel=str(row.get('Channel', ''))
                    )
                    io_points.append(io_point)
                    
                    # 处理电缆信息
                    cable_name = str(row.get('Cable_Name', ''))
                    if cable_name and cable_name not in cables:
                        cables[cable_name] = {
                            'name': cable_name,
                            'io_points': [],
                            'pair_size': int(row.get('Pair_Size', 1))
                        }
                    
                    if cable_name:
                        cables[cable_name]['io_points'].append(io_point)
                        
                except Exception as e:
                    self.logger.warning(f"处理IODB行数据失败 (行{index}): {e}")
                    continue
            
            self.logger.info(f"成功处理 {len(io_points)} 个I/O点，{len(cables)} 条电缆")
            self.logger.info(f"成功加载IODB数据: {len(io_points)}个I/O点，{len(cables)}条电缆")
            
            return {
                'io_points': io_points,
                'cables': cables,
                'summary': {
                    'total_points': len(io_points),
                    'total_cables': len(cables)
                }
            }
            
        except Exception as e:
            self.logger.error(f"加载IODB数据失败: {e}")
            raise
    
    def load_pidb_data(self, file_path: str) -> Dict[str, Any]:
        """
        加载PIDB数据
        
        Args:
            file_path: PIDB文件路径
            
        Returns:
            PIDB数据字典
        """
        self.logger.info(f"加载PIDB数据: {file_path}")
        
        try:
            # 使用Excel读取器读取文件
            excel_data = self.excel_reader.read_excel_file(file_path)
            
            # 简化处理，返回基本结构
            return {
                'data': excel_data,
                'summary': {
                    'loaded': True
                }
            }
            
        except Exception as e:
            self.logger.error(f"加载PIDB数据失败: {e}")
            raise
    
    def create_data_models(self, iodb_data: Dict[str, Any], pidb_data: Dict[str, Any], 
                          cabinet_profiles: Dict[str, Any], wiring_typicals: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建数据模型对象
        
        Args:
            iodb_data: IODB数据
            pidb_data: PIDB数据
            cabinet_profiles: 机柜配置
            wiring_typicals: 典型回路
            
        Returns:
            数据模型字典
        """
        self.logger.info("创建数据模型对象")
        
        try:
            # 从IODB数据提取信息
            io_points = iodb_data.get('io_points', [])
            cables_dict = iodb_data.get('cables', {})
            cables = list(cables_dict.values())
            
            # 创建机柜对象
            cabinets = []
            for cabinet_name, cabinet_config in cabinet_profiles.items():
                cabinet_obj = {
                    'name': cabinet_name,
                    'config': cabinet_config,
                    'rails': cabinet_config.get('rails', [])
                }
                cabinets.append(cabinet_obj)
                self.logger.debug(f"机柜 {cabinet_name} 创建成功，包含 {len(cabinet_obj['rails'])} 个导轨")
            
            # 创建典型回路对象
            wiring_typical_objects = []
            for typical_name, typical_config in wiring_typicals.items():
                # 创建一个简单的对象类来模拟典型回路
                class WiringTypical:
                    def __init__(self, name, config):
                        self.name = name
                        self.config = config
                        self.components = config.get('components', [])

                typical_obj = WiringTypical(typical_name, typical_config)
                wiring_typical_objects.append(typical_obj)
            
            self.logger.info(f"数据模型创建完成: {len(io_points)}个I/O点, {len(cables)}条电缆, {len(cabinets)}个机柜, {len(wiring_typical_objects)}个典型回路")
            
            return {
                'io_points': io_points,
                'cables': cables,
                'cabinets': cabinets,
                'wiring_typicals': wiring_typical_objects
            }
            
        except Exception as e:
            self.logger.error(f"创建数据模型失败: {e}")
            raise
    
    def _parse_signal_type(self, signal_type_str: str) -> SignalType:
        """
        解析信号类型字符串
        
        Args:
            signal_type_str: 信号类型字符串
            
        Returns:
            SignalType枚举值
        """
        try:
            return SignalType(signal_type_str.upper())
        except ValueError:
            self.logger.warning(f"未知信号类型: {signal_type_str}，使用默认值DI")
            return SignalType.DI
